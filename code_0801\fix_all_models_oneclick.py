#!/usr/bin/env python3
"""
一键启动视频处理修复脚本
自动按顺序处理所有模型：7B → 72B → 3B
直接输出API返回的完整内容，无需复杂解析
"""

import cv2
import base64
import time
from openai import OpenAI
import os
import pandas as pd
import json
from datetime import datetime
import traceback

def has_valid_output(output_value):
    """检查Output字段是否有有效内容"""
    if pd.isna(output_value):
        return False
    if str(output_value).strip() == '' or str(output_value).lower() == 'nan':
        return False
    return True

def save_progress(model_name, processed_videos, total_videos):
    """保存处理进度到文件"""
    progress_file = f'code_0801/result/{model_name}_progress.json'
    progress_data = {
        'model': model_name,
        'processed_videos': list(processed_videos),
        'total_videos': total_videos,
        'last_update': datetime.now().isoformat(),
        'completion_rate': len(processed_videos) / total_videos if total_videos > 0 else 0
    }
    
    os.makedirs('code_0801/result', exist_ok=True)
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(progress_data, f, indent=2, ensure_ascii=False)

def load_progress(model_name):
    """从文件加载处理进度"""
    progress_file = f'code_0801/result/{model_name}_progress.json'
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            return set(progress_data.get('processed_videos', []))
        except Exception as e:
            print(f"加载进度文件失败: {e}")
            return set()
    return set()

def identify_videos_needing_fix(model_name):
    """智能识别真正需要修复的视频"""
    print(f"\n{'='*60}")
    print(f"🔍 智能分析需要修复的视频: {model_name}")
    print(f"{'='*60}")
    
    # 加载数据文件
    progress_file = f'code_0801/result/{model_name}_progress.json'
    output_file = f'code_0801/result/{model_name}_trainset_output.csv'
    
    if not os.path.exists(progress_file) or not os.path.exists(output_file):
        print(f"❌ 错误: 找不到必要的文件")
        return []
    
    # 加载已处理视频列表
    with open(progress_file, 'r', encoding='utf-8') as f:
        progress_data = json.load(f)
    processed_videos = set(progress_data.get('processed_videos', []))
    
    # 加载输出数据
    df = pd.read_csv(output_file, index_col=0)
    
    # 智能识别需要修复的视频
    videos_needing_fix = []
    videos_with_valid_output = 0
    videos_with_invalid_output = 0
    
    print(f"📊 分析 {len(processed_videos)} 个已标记为处理完成的视频...")
    
    for video_id in processed_videos:
        video_data = df[df['video_id'] == video_id]
        if video_data.empty:
            videos_needing_fix.append(video_id)
            videos_with_invalid_output += 1
            continue
        
        # 检查该视频的所有Output字段
        outputs = video_data['Output']
        has_any_valid_output = any(has_valid_output(output) for output in outputs)
        
        if not has_any_valid_output:
            videos_needing_fix.append(video_id)
            videos_with_invalid_output += 1
        else:
            videos_with_valid_output += 1
    
    print(f"📈 分析结果:")
    print(f"  ✅ 有有效输出的视频: {videos_with_valid_output} 个")
    print(f"  🔧 需要修复的视频: {videos_with_invalid_output} 个")
    print(f"  📊 修复比例: {videos_with_invalid_output}/{len(processed_videos)} ({videos_with_invalid_output/len(processed_videos)*100:.1f}%)")
    
    return videos_needing_fix

def fix_single_model(model_name, client, folder_path, QA_df):
    """修复单个模型的所有问题视频"""
    print(f"\n{'='*60}")
    print(f"🚀 开始处理模型: {model_name}")
    print(f"{'='*60}")
    
    # 智能识别需要修复的视频
    videos_to_fix = identify_videos_needing_fix(model_name)
    
    if not videos_to_fix:
        print(f"✅ 模型 {model_name} 无需修复，所有视频都有有效输出！")
        return True
    
    print(f"\n🔧 开始修复 {len(videos_to_fix)} 个视频...")
    
    # 数据保存路径
    res_path = f'code_0801/result/{model_name}_trainset_output.csv'
    
    # 加载现有结果DataFrame
    res = pd.read_csv(res_path, index_col=0)
    
    fixed_count = 0
    failed_videos = []
    
    # 遍历需要修复的视频
    for i, select_vid_name in enumerate(videos_to_fix):
        print(f'\n[{model_name}] 🎬 修复视频 {i + 1}/{len(videos_to_fix)}: {select_vid_name}')
        
        # 检查视频文件是否存在
        video_path = os.path.join(folder_path, str(select_vid_name))
        if not os.path.exists(video_path):
            print(f"❌ 警告: 视频文件不存在 {video_path}")
            failed_videos.append(select_vid_name)
            continue
        
        # 检查该视频是否在数据集中
        temp_QA = res[res['video_id'] == select_vid_name].copy()
        if temp_QA.empty:
            print(f"❌ 警告: 视频 {select_vid_name} 不在数据集中")
            failed_videos.append(select_vid_name)
            continue

        try:
            # 读取视频帧
            video = cv2.VideoCapture(video_path)
            base64Frames = []
            while video.isOpened():
                success, frame = video.read()
                if not success:
                    break
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
            video.release()
            
            print(f"📹 读取帧数: {len(base64Frames)}")

            if len(base64Frames) == 0:
                print(f"❌ 警告: 视频 {select_vid_name} 无法读取帧")
                failed_videos.append(select_vid_name)
                continue

            # 构建提示词
            prompt = "Please assume the role of an agent. The video represents your egocentric observations from the past to the present. Please answer the following questions: \n"

            for temp_QA_idx in range(temp_QA.shape[0]):
                qa = temp_QA['question'].iloc[temp_QA_idx]
                prompt += "<QA%d: %s> \n" % (temp_QA_idx, qa)

            prompt += "The template for the answer is: \
                            <QA0: Think: []; Option: []. \n QA1: Think: []; Option: []. \n QA2: Think: []; Option: []. \n QA3: ...>\n\
                            where the Think explains why you choose this option and your thinking process. The Option only outputs one option from 'A' to 'E' here, do not output redundant content. "

            # 准备API调用内容
            video_content = []
            for buffer in base64Frames:
                video_content.append(f"data:image/jpeg;base64,{buffer}")
            
            content = [
                {
                    "type": "video",
                    "video": video_content
                },
                {
                    "type": "text",
                    "text": prompt
                }
            ]

            PROMPT_MESSAGES = [
                {
                    "role": "user",
                    "content": content
                }
            ]
            
            print("🔄 正在调用API...")
            result = client.chat.completions.create(
                model=model_name,
                messages=PROMPT_MESSAGES
            )
            print("✅ API调用成功")
            
            # 🎯 关键改进：直接使用完整的API返回内容，不进行复杂解析
            api_response = result.choices[0].message.content
            
            # 为该视频的所有问题设置相同的完整输出
            for idx in temp_QA.index:
                res.loc[idx, 'Output'] = api_response
            
            # 保存结果
            res.to_csv(res_path)
            fixed_count += 1
            print(f"✅ 视频 {select_vid_name} 修复成功")
            
            # 短暂延迟避免API限制
            time.sleep(1)

        except Exception as e:
            print(f"❌ 处理视频 {select_vid_name} 时发生错误: {e}")
            print(f"🔍 错误详情: {traceback.format_exc()}")
            failed_videos.append(select_vid_name)
            time.sleep(5)  # 出错时等待更长时间
            continue

    # 输出最终统计
    print(f"\n{'='*60}")
    print(f"📊 模型 {model_name} 修复完成统计:")
    print(f"✅ 成功修复: {fixed_count} 个视频")
    print(f"❌ 修复失败: {len(failed_videos)} 个视频")
    
    if fixed_count + len(failed_videos) > 0:
        success_rate = fixed_count/(fixed_count+len(failed_videos))*100
        print(f"📈 成功率: {success_rate:.1f}%")
    
    if failed_videos:
        failed_file = f"failed_{model_name.replace('-', '_')}_videos.txt"
        with open(failed_file, 'w', encoding='utf-8') as f:
            for video in failed_videos:
                f.write(f"{video}\n")
        print(f"📝 失败视频列表已保存到: {failed_file}")
    
    print(f"💾 结果已保存至: {res_path}")
    
    return len(failed_videos) == 0

def main():
    """一键启动所有模型的修复流程"""
    print("🎯 一键启动视频处理修复系统")
    print("=" * 60)
    
    # 初始化OpenAI客户端
    client = OpenAI(
        api_key="sk-48dc15715aa94943958ebced812f6fa0",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    # 数据路径
    folder_path = r'E:\Thu实习\code_0801\video_keyframes_min4f'
    QA_df = pd.read_json(r'E:\Thu实习\code_0801\train_set_option_shuffle.json')

    # 按优先级顺序处理模型
    models = [
        "qwen2.5-vl-7b-instruct",   # 第一优先级
        "qwen2.5-vl-72b-instruct",  # 第二优先级
        "qwen2.5-vl-3b-instruct"    # 第三优先级
    ]
    
    start_time = datetime.now()
    results = {}
    
    for i, model_name in enumerate(models, 1):
        print(f"\n🚀 开始处理第 {i} 个模型: {model_name}")
        model_start_time = datetime.now()
        
        try:
            success = fix_single_model(model_name, client, folder_path, QA_df)
            model_end_time = datetime.now()
            model_duration = model_end_time - model_start_time
            
            results[model_name] = {
                'success': success,
                'duration': model_duration,
                'status': '✅ 成功' if success else '⚠️ 部分失败'
            }
            
            print(f"⏱️ 模型 {model_name} 处理耗时: {model_duration}")
            
        except Exception as e:
            model_end_time = datetime.now()
            model_duration = model_end_time - model_start_time
            
            results[model_name] = {
                'success': False,
                'duration': model_duration,
                'status': f'❌ 失败: {str(e)}'
            }
            
            print(f"❌ 模型 {model_name} 处理失败: {e}")
            print(f"🔍 错误详情: {traceback.format_exc()}")
    
    # 输出最终汇总报告
    end_time = datetime.now()
    total_duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("🎉 所有模型处理完成！最终汇总报告")
    print(f"{'='*60}")
    print(f"⏱️ 总耗时: {total_duration}")
    print(f"📊 处理结果:")
    
    for model_name, result in results.items():
        print(f"  {result['status']} {model_name} (耗时: {result['duration']})")
    
    successful_models = sum(1 for r in results.values() if r['success'])
    print(f"\n📈 总体成功率: {successful_models}/{len(models)} ({successful_models/len(models)*100:.1f}%)")
    
    print(f"\n🎯 一键修复任务全部完成！")

if __name__ == '__main__':
    main()
